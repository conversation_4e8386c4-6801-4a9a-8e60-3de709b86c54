import Login from "./pages/Login"
import { Routes, Route } from "react-router-dom"
import Signup from "./pages/Signup"
import GetCurrentUser from "./functions/getCurrentUser"
import Home from "./pages/Home"
import { useEffect } from "react"





const App = () => {
  GetCurrentUser()
  useEffect(() => {
    console.log(
      "%cStop!",
      "color: red; font-size: 48px; font-weight: bold;"
    );
    console.log(
      "%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or 'hack' someone's account, it is a scam and will give them access to your Instagram account.\n\nSee https://www.facebook.com/selfxss for more information.",
      "color: white; font-size: 20px; letter-spacing: 2px;"
    );
  }, []);
  return (
    <>
    <Routes>
      <Route path="/login" element={<Login/>}/>
      <Route path="/signup" element={<Signup/>}/>
      <Route path="/signup/:username" element={<Signup/>}/>
      <Route path="/" element={<Home/>}/>
    </Routes>
      
    </>
  )
}

export default App
