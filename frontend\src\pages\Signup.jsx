
import { useEffect, useRef, useState } from "react";
import { BiLogoFacebookSquare } from "react-icons/bi";
import { useNavigate } from 'react-router-dom';
import axios from "axios";
import { RxCrossCircled } from "react-icons/rx";
import { FaRegCircleCheck } from "react-icons/fa6";
import LoginFooter from "../components/LoginFooter";






const Signup = () => {


  const inputRef1 = useRef(null)
  const inputRef2 = useRef(null)
  const inputRef3 = useRef(null)
  const inputRef4 = useRef(null)
  const inputBox = useRef(null)
  const inputBox1 = useRef(null)
  const inputBox3 = useRef(null)
  const inputBox2 = useRef(null)
  const [username, setUsername] = useState('')
  const [pass, setPassword] = useState('')
  const [show, setShow] = useState(false)
  const [email, setEmail] = useState('')
  const [name, setName] = useState('')
  const [valid, setValid] = useState(true);
  const [PassValid, setPassValid] = useState(true);
  const [loading, setLoading] = useState(false)
  const serverUrl = import.meta.env.VITE_SERVER_URL
  const navigate = useNavigate()
  const timerRef = useRef(null);
  const usernameRef = useRef(null);
  const passwordRef = useRef(null);




  useEffect(() => {
    inputBox.current.focus()
    document.title = `Sign up - Instagram`;

  }, [])

  const handleSignup = async (e) => {
    e.preventDefault()
    setLoading(true)
    try {
      let result = await axios.post(`${serverUrl}/api/auth/register`, {
        username,
        email,
        password: pass,
        name
      }, { withCredentials: true })
      console.log(result)
      setLoading(false)
      navigate('/')
      setEmail('')
      setPassword('')
      setName('')
      setUsername('')
    } catch (error) {
      console.log(error);
      setLoading(false)
    }

  }

  useEffect(() => {
    if (pass === '') {
      inputRef2.current.style.fontSize = '';
      inputRef2.current.style.top = '';
      inputBox1.current.style.paddingTop = '';
    }
  }, [pass]);
  useEffect(() => {
    if (username === '') {
      inputRef4.current.style.fontSize = '';
      inputRef4.current.style.top = '';
      inputBox3.current.style.paddingTop = '';
    }
  }, [username]);
  useEffect(() => {
    if (name === '') {
      inputRef3.current.style.fontSize = '';
      inputRef3.current.style.top = '';
      inputBox2.current.style.paddingTop = '';
    }
  }, [name]);
  useEffect(() => {
    if (email === '') {
      inputRef1.current.style.fontSize = '';
      inputRef1.current.style.top = '';
      inputBox.current.style.paddingTop = '';
    }
  }, [email]);

  const handleinput1 = () => {
    inputRef1.current.style.fontSize = '10px';
    inputRef1.current.style.top = '4px';
    inputBox.current.style.paddingTop = '12px';
  }

  const handleinput2 = () => {
    inputRef2.current.style.fontSize = '10px';
    inputRef2.current.style.top = '4px';
    inputBox1.current.style.paddingTop = '12px';
  }

  const handleinput3 = () => {
    inputRef3.current.style.fontSize = '10px';
    inputRef3.current.style.top = '4px';
    inputBox2.current.style.paddingTop = '12px';
  }

  const handleinput4 = () => {
    inputRef4.current.style.fontSize = '10px';
    inputRef4.current.style.top = '4px';
    inputBox3.current.style.paddingTop = '12px';
  }

  const handleblur = () => {
    if (inputBox.current.value === '') {
      inputRef1.current.style.fontSize = '';
      inputRef1.current.style.top = '';
      inputBox.current.style.paddingTop = '';
    }
  }

  const handleblur2 = () => {
    if (inputBox1.current.value === '') {
      inputRef2.current.style.fontSize = '';
      inputRef2.current.style.top = '';
      inputBox1.current.style.paddingTop = '';
    }
  }

  const handleblur3 = () => {
    if (inputBox2.current.value === '') {
      inputRef3.current.style.fontSize = '';
      inputRef3.current.style.top = '';
      inputBox2.current.style.paddingTop = '';
    }
  }

  const handleblur4 = () => {
    if (inputBox3.current.value === '') {
      inputRef4.current.style.fontSize = '';
      inputRef4.current.style.top = '';
      inputBox3.current.style.paddingTop = '';
    }
  }
  const handleShow = () => {
    setShow(!show)
    setTimeout(() => {
      setShow(false)
    }, 1000)
  }

  const validateEmail = (value) => {
    setEmail(value);
    clearTimeout(timerRef.current);

    if (value === '') {
      setValid(true)
    } else {
      timerRef.current = setTimeout(() => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        setValid(emailRegex.test(value));
      }, 1000);
    }
  };

  const handlePassword = (value) => {
    setPassword(value);
    clearTimeout(passwordRef.current);


    if (value === '') {
      setPassValid(true)
    } else {
      passwordRef.current = setTimeout(() => {
        const passRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/;
        setPassValid(passRegex.test(value));
      }, 1000);
    }
  };


  const handleUsername = (e) => {
    const value = e.target.value.toLowerCase();
    setUsername(value);
    clearTimeout(usernameRef.current);

    usernameRef.current = setTimeout(() => {
      if (value.length > 0) {
        navigate(`/signup/:${value}`);

      }
    }, 700);
  };


  passwordRef



  return (
    <div className='w-full min-h-screen flex flex-col justify-start items-center bg-black pt-2 md:pt-3'>

      <div className='flex flex-col justify-center items-center md:border-1 border-[#363636] p-10'>
        <h1 className='heading text-5xl tracking-tight font-medium text-gray-100 mb-5 pt-3'>Instagram</h1>
        <form className='flex items-center flex-col justify-center '>
          <p className='text-[#c5c1bcc4] text-[16px] font-semibold mb-2 text-center leading-5'>Sign up to see photos and videos<br /> from your friends.</p>
          <button className="flex items-end px-12 py-[6px] rounded-lg gap-2 bg-[#0095f6] text-[#fffafd] font-semibold text-sm mt-2 transition-all duration-200 ease-in-out hover:bg-[#2d6dd6b7] active:scale-95"><BiLogoFacebookSquare size={22} /><p >Log in with Facebook</p></button>
          <div className='flex items-center justify-center gap-4 mt-4'>
            <hr className='w-[108px] h-[2px] bg-[#55555574] ' />
            <p className='text-[#ffffffa5] text-xs '>OR</p>
            <hr className='w-[108px] h-[2px] bg-[#55555574]' />
          </div>
          <div className="flex flex-col pt-4 gap-2">
            <div onKeyDown={handleinput1} className='relative'>
              <input required ref={inputBox} onBlur={handleblur} type="email" value={email} onChange={(e) => validateEmail(e.target.value)} className={`${!valid ? 'border-red-500 border-1' : ''} w-[270px] h-[36px]  pl-3 border border-[#555555] outline-none text-xs text-white bg-[#121212] rounded-sm  `} />
              <div ref={inputRef1} onClick={() => { inputBox.current.focus() }} className='absolute top-[9px] left-3 text-xs z-1  transition-all duration-300 ease-in-out'>
                <div className='flex gap-41 justify-center '>
                  <p className='text-[#b0abab]'>Email Address</p>
                  {!valid && <RxCrossCircled size={24} className="text-red-500 pt-1" />}
                </div>
              </div>
              {(valid && email.length > 4) && <div className='absolute flex items-center justify-center gap-2 top-2 right-3 '>
                <FaRegCircleCheck size={22} className="text-[#909090]" />
              </div>}
              {!valid && <p className="text-red-500  text-xs py-2 px-2">Enter a valid email address.</p>}
            </div>
            <div onKeyDown={handleinput2} className='relative'>
              <input required ref={inputBox1} onBlur={handleblur2} type={show ? 'text' : 'password'} value={pass} onChange={e => handlePassword(e.target.value)} className={`${!PassValid ? 'border-red-500 border-1' : ''} w-[270px] h-[36px] border pl-3 border-[#555555] outline-none text-xs text-white bg-[#121212] rounded-sm`} />
              <div ref={inputRef2} onClick={() => { inputBox1.current.focus() }} className='absolute top-[9px] left-3 text-xs z-1 transition-all duration-300 ease-in-out'>
                <p className='text-[#b0abab]'>Password</p>
              </div>
              {pass.length > 0 && <div className={`absolute flex items-center justify-center gap-2 right-3 ${!PassValid ? 'top-1' : 'top-2 '}`}>
                {(pass.length >= 5 && PassValid) ? <FaRegCircleCheck size={22} className="text-[#909090]" /> : <RxCrossCircled size={26} className="text-red-500 pt-[2px]" />}
                <p onClick={() => { handleShow() }} className={` cursor-pointer text-white font-semibold text-sm transition-all duration-300 ease-in-out hover:text-[#919191]`}>{show ? 'Hide' : 'Show'}</p>
              </div>}
            </div>
            {!PassValid && <p className="text-red-500  text-xs py-1 px-2">This password is too easy to guess. Please create<br /> a new one.</p>}
            <div onKeyDown={handleinput3} className='relative'>
              <input ref={inputBox2} onBlur={handleblur3} type="text" value={name} onChange={(e) => setName(e.target.value)} className='w-[270px] h-[36px]  pl-3 border border-[#555555] outline-none text-xs text-white bg-[#121212] rounded-sm ' />
              <div ref={inputRef3} onClick={() => { inputBox.current.focus() }} className='absolute top-[9px] left-3 text-xs z-1 transition-all duration-300 ease-in-out'>
                <p className='text-[#b0abab]'>Full Name</p>
              </div>
              {name.length >= 3 && <div className='absolute flex items-center justify-center gap-2 top-2 right-3 '>
                <FaRegCircleCheck size={22} className="text-[#909090]" />
              </div>}
            </div>
            <div onKeyDown={handleinput4} className='relative'>
              <input required ref={inputBox3} onBlur={handleblur4} type="text" value={username} onChange={handleUsername} className='w-[270px] h-[36px]  pl-3 border border-[#555555] outline-none text-xs text-white bg-[#121212] rounded-sm  ' />
              <div ref={inputRef4} onClick={() => { inputBox.current.focus() }} className='absolute top-[9px] left-3 text-xs z-1 transition-all duration-300 ease-in-out'>
                <p className='text-[#b0abab]'>Username</p>
              </div>
            </div>

          </div>
          <div>
            <p className='text-[#ffffffa5] text-xs mt-4 text-center'>People who use our service may have uploaded<br /> your contact information to Instagram. <span className='text-[#708dff] cursor-pointer'>Learn<br /> More</span></p>
            <p className='text-[#ffffffa5] text-xs mt-4 text-center'>By signing up, you agree to our <span className='text-[#708dff] cursor-pointer'>Terms</span> , <span className='text-[#708dff] cursor-pointer'> Privacy<br /> Policy</span > and <span className='text-[#708dff] cursor-pointer'>Cookies Policy</span> .</p>
          </div>
          <button disabled={username === '' || pass === '' || email === ''} onClick={handleSignup} className={`${username === '' || email === '' || pass.length < 5 ? 'bg-[#0069ad] text-[#aaafb3]' : 'bg-[#4a8df9] hover:bg-[#4a5ef9b7] text-white cursor-pointer active:scale-95'
            } w-[270px] h-[34px] rounded-lg font-semibold text-sm mt-4 transition-all duration-200 flex items-center justify-center`}
          >
            {loading ? (
              <div className="w-4 h-4 border-t-1 border-b-1 border-white rounded-full animate-spin "></div>
            ) : (
              'Sign up'
            )}
          </button>
        </form>
      </div>
      <div className='md:border-1 border-[#363636] flex flex-col items-center justify-center py-6 bg-black w-full md:w-fit md:px-[121px] mt-[10px] '>
        <p onClick={() => { navigate('/login') }} className='text-[#ffffffe9] text-sm text-center leading-3'>Have an account? <br /><span className='text-[#007fce] cursor-pointer font-semibold text-sm'>Log in</span> </p>
      </div>
      <LoginFooter page="signUp" />
    </div>
  )
}

export default Signup
